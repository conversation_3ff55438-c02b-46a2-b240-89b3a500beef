
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>畅行桂林停车费计算器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        :root {
            --primary: #3498db;
            --secondary: #2c3e50;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #34495e;
            --success: #2ecc71;
            --warning: #f39c12;
            --stage3: #9b59b6;
            --free: #2ecc71;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: #fff;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            width: 1200px; /* 固定宽度为1200px */
            background-color: rgba(44, 62, 80, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
            overflow: hidden;
            margin: 0 auto;
        }

        header {
            background: linear-gradient(45deg, #1a2980, #26d0ce);
            padding: 25px;
            text-align: center;
            border-bottom: 3px solid var(--primary);
            position: relative;
            overflow: hidden;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            text-shadow: 0 3px 6px rgba(0,0,0,0.4);
        }

        .subtitle {
            font-size: 1.1rem;
            color: rgba(255,255,255,0.85);
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            position: relative;
            font-weight: 300;
        }

        .calculator {
            display: flex;
            flex-direction: column;
            gap: 25px;
            padding: 25px;
        }

        .panel-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .top-section {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
        }

        .panel {
            background: rgba(52, 73, 94, 0.8);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255,255,255,0.1);
        }



        .input-group {
            margin-bottom: 0;
        }

        .panel-title {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--primary);
        }

            .panel-title i {
                margin-right: 12px;
                color: var(--primary);
            }

        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--light);
        }

        input, select {
            width: 100%;
            padding: 14px;
            border-radius: 8px;
            border: 2px solid rgba(255,255,255,0.2);
            background-color: rgba(0,0,0,0.2);
            font-size: 1.1rem;
            transition: all 0.3s;
            color: white;
        }

            input::placeholder {
                color: #95a5a6;
            }

            input:focus {
                border-color: var(--primary);
                outline: none;
                box-shadow: 0 0 8px rgba(52, 152, 219, 0.7);
                background-color: rgba(0,0,0,0.3);
            }

        .help-text {
            font-size: 0.85rem;
            color: #95a5a6;
            margin-top: 5px;
            padding-left: 5px;
        }

        .time-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

            .time-input-group input {
                width: 50%;
            }

            .time-input-group span {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
            }

        .seconds-input {
            width: 100px !important;
        }

        .result-panel {
            background: rgba(23, 32, 42, 0.9);
            border-radius: 12px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

            .result-panel::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--primary), var(--success), var(--warning));
                z-index: 1;
            }

        .result-display {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            padding: 25px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.4);
            width: 100%;
            margin-bottom: 25px;
            border: 2px dashed rgba(255,255,255,0.2);
            text-shadow: 0 3px 6px rgba(0,0,0,0.4);
        }

        .details {
            width: 100%;
            background: rgba(255, 255, 255, 0.08);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-title {
            color: #bdc3c7;
        }

        .detail-value {
            font-weight: 600;
        }

        .stage1 {
            color: var(--primary);
        }

        .stage2 {
            color: var(--warning);
        }

        .stage3 {
            color: var(--stage3);
        }

        .free {
            color: var(--free);
        }

        .btn {
            padding: 16px 30px;
            background: linear-gradient(45deg, var(--primary), #2980b9);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 15px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            position: relative;
        }

            .btn:hover {
                background: #2980b9;
                transform: translateY(-3px);
                box-shadow: 0 6px 12px rgba(0,0,0,0.4);
            }

            .btn:active {
                transform: translateY(1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .btn i {
                margin-right: 10px;
            }

        .examples {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .example-btn {
            background: rgba(52, 73, 94, 0.8);
            border: 2px solid var(--primary);
            color: var(--light);
        }

        .rules {
            background: rgba(44, 62, 80, 0.9);
            padding: 25px;
            margin-top: 20px;
            border-top: 1px solid var(--primary);
        }

            .rules h3 {
                font-size: 1.5rem;
                margin-bottom: 15px;
                color: var(--primary);
                text-align: center;
            }

            .rules ul {
                padding-left: 25px;
                line-height: 1.8;
            }

            .rules li {
                margin-bottom: 12px;
                position: relative;
                padding-left: 25px;
            }

                .rules li::before {
                    content: '•';
                    color: var(--primary);
                    position: absolute;
                    left: 0;
                    top: 0;
                    font-size: 1.4rem;
                }

        .highlight {
            background: rgba(231, 76, 60, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            color: var(--accent);
            font-weight: 600;
        }

        .error-message {
            color: #e74c3c;
            font-weight: bold;
            padding: 5px;
            margin-top: 5px;
            display: none;
        }

        .billing-list {
            width: 100%;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .billing-header {
            display: flex;
            justify-content: space-between;
            padding: 10px 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 0.95rem;
        }

        .billing-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .billing-time {
            flex: 2;
        }

        .billing-duration {
            flex: 1;
            text-align: center;
        }

        .billing-fee {
            flex: 1;
            text-align: right;
            font-weight: bold;
        }

        .free-item {
            color: var(--free);
        }

        .stage1-item {
            color: var(--primary);
        }

        .stage2-item {
            color: var(--warning);
        }

        .stage3-item {
            color: var(--stage3);
        }

        .billing-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary);
            text-align: center;
        }

        .new-highlight {
            background-color: rgba(46, 204, 113, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            color: #2ecc71;
            font-weight: bold;
        }

        .quick-time-label {
            position: relative;
            padding-left: 30px;
        }

            .quick-time-label i {
                position: absolute;
                left: 5px;
                top: 50%;
                transform: translateY(-50%);
                color: var(--primary);
            }

        .quick-input-group {
            position: relative;
        }

        .format-hint {
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            font-size: 0.8rem;
            color: #95a5a6;
            background: rgba(255,255,255,0.9);
            padding: 0 8px;
            border-radius: 4px;
            pointer-events: none;
            color: #2c3e50;
        }

        .quick-input-panel {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .quick-input-group {
            flex: 1;
            min-width: 250px;
        }

        .day-total {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed rgba(255,255,255,0.3);
            font-weight: bold;
            color: #f1c40f;
            font-size: 1.1rem;
        }

        .fix-highlight {
            background-color: rgba(155, 89, 182, 0.3);
            padding: 5px 12px;
            border-radius: 20px;
            color: #9b59b6;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 8px;
            background: var(--accent);
            color: white;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            width: 100%;
            margin-bottom: 20px;
        }

        .summary-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .summary-title {
            font-size: 0.9rem;
            color: #bdc3c7;
            margin-bottom: 8px;
        }

        .summary-value {
            font-size: 1.4rem;
            font-weight: bold;
            color: var(--success);
        }

        .stage-card .summary-value {
            color: var(--primary);
        }

        /* 计费规则切换按钮样式 */
        .billing-type-selector {
            display: flex;
            justify-content: center;
            margin: 15px 0;
            gap: 15px;
            flex-wrap: wrap;
        }

        .billing-type-btn {
            padding: 12px 20px;
            background: rgba(52, 152, 219, 0.3);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
            font-weight: 600;
            min-width: 150px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

            .billing-type-btn i {
                margin-right: 8px;
            }

            .billing-type-btn.active {
                background: rgba(52, 152, 219, 0.8);
                border-color: #fff;
                box-shadow: 0 0 10px rgba(52, 152, 219, 0.7);
            }

            .billing-type-btn:hover {
                background: rgba(52, 152, 219, 0.5);
            }

        .rule-highlight {
            background-color: rgba(155, 89, 182, 0.3);
            padding: 5px 10px;
            border-radius: 5px;
            color: #9b59b6;
            font-weight: bold;
        }

        @media (max-width: 1250px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 20px;
                box-sizing: border-box;
            }

            body {
                padding: 10px;
            }
        }

        @media (max-width: 900px) {
            .billing-panel {
                grid-template-columns: 1fr;
            }

                .billing-panel .panel-title {
                    grid-column: 1 / span 1;
                }

            .examples {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 2rem;
            }

            .result-display {
                font-size: 2rem;
            }

            .time-input-group {
                flex-direction: column;
            }

                .time-input-group input {
                    width: 100%;
                }

            .billing-header, .billing-item {
                flex-direction: column;
                gap: 5px;
            }

            .billing-time, .billing-duration, .billing-fee {
                width: 100%;
                text-align: left;
            }

            .quick-input-group {
                min-width: 100%;
            }

            .summary-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .panel {
                padding: 18px;
            }

            .billing-type-selector {
                flex-wrap: wrap;
                justify-content: center;
            }

            .billing-type-btn {
                min-width: 130px;
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }

        /* 计费参数网格布局 */
        /* 计费参数Flex布局 */
        .billing-flex-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .first-row, .second-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

            .first-row .input-group,
            .second-row .input-group {
                flex: 1;
                min-width: 200px;
            }

            /* 确保第一行4个输入框，第二行3个输入框 */
            .first-row .input-group {
                flex-basis: calc(25% - 15px);
            }

            .second-row .input-group {
                flex-basis: calc(33.333% - 15px);
            }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .first-row .input-group,
            .second-row .input-group {
                flex-basis: calc(50% - 15px);
            }
        }

        @media (max-width: 768px) {
            .first-row .input-group,
            .second-row .input-group {
                flex-basis: 100%;
            }
        }

        /* 时间输入行布局 */
        .time-input-row {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .input-group-wrapper, .button-group-wrapper {
            flex: 1; /* 各占1/3宽度 */
            min-width: 0; /* 防止内容溢出 */
        }

        /* 输入组样式 */
        .input-group-wrapper {
            display: flex;
            flex-direction: column;
        }

        /* 按钮组样式 */
        .button-group-wrapper {
            display: flex;
            flex-direction: column;
            justify-content: flex-end; /* 底部对齐 */
        }

        .button-energy-row {
            display: flex;
            align-items: center; /* 改为垂直居中对齐 */
            justify-content: center; /* 水平居中对齐 */
            gap: 10px;
            width: 100%;
        }

            .button-energy-row .btn {
                flex: 1; /* 按钮占据剩余空间 */
                min-width: 120px; /* 确保按钮有最小宽度 */
                padding: 16px 30px; /* 保持按钮内边距 */
            }

        /* 新能源车标签样式 */
        .energy-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            white-space: nowrap;
            padding: 16px 12px; /* 增加内边距匹配按钮高度 */

            border-radius: 8px;
            color: #2ecc71;
            font-weight: 600;
            height: 100%;
        }

            .energy-label input {
                width: auto;
                margin-right: 8px;
            }

        /* 响应式调整 */
        @media (max-width: 900px) {
            .time-input-row {
                flex-direction: column;
            }

            .input-group-wrapper, .button-group-wrapper {
                width: 100%;
            }

            .button-energy-row {
                flex-direction: column;
                align-items: stretch;
            }
        }
        /**增加降费选择配置**/
        .title-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .title-text {
            display: flex;
            align-items: center;
            flex-grow: 1;
        }

        .discount-options {
            display: flex;
            gap: 15px;
            font-size: 0.95rem;
            margin-left: 20px;
            white-space: nowrap;
        }

            .discount-options label {
                display: flex;
                align-items: center;
                gap: 5px;
                cursor: pointer;
                color: #bdc3c7;
            }

            .discount-options input[type="radio"] {
                margin: 0;
                width: auto;
            }

        /* 响应式调整 */
        @media (max-width: 900px) {
            .title-container {
                flex-direction: column;
                align-items: flex-start;
            }

            .discount-options {
                margin-top: 10px;
                margin-left: 0;
                width: 100%;
                justify-content: flex-start;
            }
        }
        /****/

        footer {
            text-align: center;
            padding-top: 20px;
            color: white;
            grid-column: 1 / -1;
            opacity: 0.9;
            font-size: 0.95rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <header>
                <h1><i class="fas fa-car"></i> 畅行桂林停车费计算器 </h1>
                <div class="subtitle">
                    精确计算畅行桂林停车费用，支持免费时段、分时段计费和每日封顶规则
                </div>

                <!-- 计费规则切换按钮 -->
                <div class="billing-type-selector">
                    <div class="billing-type-btn" :class="{active: currentBillingType === 1}" @click="setBillingType(1)">
                        <i class="fas fa-cog"></i> 第一类计费规则
                    </div>
                    <div class="billing-type-btn" :class="{active: currentBillingType === 2}" @click="setBillingType(2)">
                        <i class="fas fa-exchange-alt"></i> 第二类计费规则
                    </div>
                    <div class="billing-type-btn" :class="{active: currentBillingType === 3}" @click="setBillingType(3)">
                        <i class="fas fa-flag"></i> 第三类计费规则
                    </div>

                    <div class="billing-type-btn" :class="{active: currentBillingType === 4}" @click="setBillingType(4)">
                        <i class="fas fa-sun"></i> 露天停车场
                    </div>
                    <div class="billing-type-btn" :class="{active: currentBillingType === 5}" @click="setBillingType(5)">
                        <i class="fas fa-home"></i> 室内停车场
                    </div>
                </div>
            </header>

            <div class="calculator">
                <div class="panel-container">
                    <div class="top-section">
                        <!-- 计费参数配置面板 -->
                        <div class="panel billing-panel">
                            <div class="panel-title">
                                <div class="title-container">
                                    <div class="title-text">
                                        <i class="fas fa-sliders-h"></i> 计费参数设置
                                    </div>
                                    <div class="discount-options">
                                        <label>
                                            <input type="radio" value="before" v-model="discountType" @change="setDiscountType('before')">
                                            现行计费规则
                                        </label>
                                        <label>
                                            <input type="radio" value="after" v-model="discountType" @change="setDiscountType('after')">
                                            优惠后规则(即将实施)
                                        </label>
                                    </div>
                                </div>
                            </div>


                            <div class="billing-grid">
                                <!-- 第一行：4个输入框 -->
                                <div class="first-row">
                                    <div class="input-group">
                                        <label for="free-minutes">免费停放时间 (分钟)</label>
                                        <input type="number" id="free-minutes" min="0" v-model="freeMinutes">
                                    </div>

                                    <div class="input-group">
                                        <label for="daily-cap">每日最高限价 (元)</label>
                                        <input type="number" id="daily-cap" min="0" v-model="dailyCap">
                                    </div>

                                    <div class="input-group">
                                        <label for="charge-start">每日计费开始时间</label>
                                        <input type="time" id="charge-start" v-model="chargeStart">
                                    </div>

                                    <div class="input-group">
                                        <label for="charge-end">每日计费结束时间</label>
                                        <input type="time" id="charge-end" v-model="chargeEnd">
                                    </div>
                                </div>

                                <!-- 第二行：3个费率输入框 -->
                                <div class="second-row" style="margin-top:15px;">
                                    <div class="input-group">
                                        <label for="first-hour-rate" id="first-hour-label">{{ firstHourLabel }}</label>
                                        <input type="number" id="first-hour-rate" min="0" step="0.1" v-model="firstHourRate">
                                        <p class="help-text" id="first-hour-help">{{ firstHourHelp }}</p>
                                    </div>

                                    <div class="input-group">
                                        <label for="second-hour-rate" id="second-hour-label">{{ secondHourLabel }}</label>
                                        <input type="number" id="second-hour-rate" min="0" step="0.1" v-model="secondHourRate">
                                        <p class="help-text" id="second-hour-help">{{ secondHourHelp }}</p>
                                    </div>

                                    <div class="input-group">
                                        <label for="third-hour-rate" id="third-hour-label">{{ thirdHourLabel }}</label>
                                        <input type="number" id="third-hour-rate" min="0" step="0.1" v-model="thirdHourRate">
                                        <p class="help-text" id="third-hour-help">{{ thirdHourHelp }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 停车时间面板 -->
                        <div class="panel">
                            <div class="panel-title">
                                <i class="fas fa-clock"></i> 停车时间
                            </div>

                            <div class="time-input-row">
                                <!-- 入场时间输入组 -->
                                <div class="input-group-wrapper">
                                    <label class="quick-time-label">
                                        <i class="fas fa-sign-in-alt"></i>
                                        入场时间
                                    </label>
                                    <div style="position: relative;">
                                        <input type="text" id="quick-start" placeholder="YYYY-MM-DD HH:mm:ss" v-model="quickStart">
                                    </div>
                                    <div id="quick-start-error" class="error-message" v-if="quickStartError">{{ quickStartError }}</div>
                                </div>

                                <!-- 出车时间输入组 -->
                                <div class="input-group-wrapper">
                                    <label class="quick-time-label">
                                        <i class="fas fa-sign-out-alt"></i>
                                        出车时间
                                    </label>
                                    <div style="position: relative;">
                                        <input type="text" id="quick-end" placeholder="YYYY-MM-DD HH:mm:ss" v-model="quickEnd">
                                    </div>
                                    <div id="quick-end-error" class="error-message" v-if="quickEndError">{{ quickEndError }}</div>
                                </div>

                                <!-- 计算按钮组 -->
                                <div class="button-group-wrapper">
                                    <div class="button-energy-row">
                                        <button class="btn" @click="calculateFee" :disabled="isCalculating">
                                            <span class="btn-text"><i class="fas fa-calculator"></i> 计算费用</span>
                                        </button>

                                        <div id="xin_neng_yuan" v-if="currentBillingType !== 4 && currentBillingType !== 5" >
                                            <label for="new-energy-vehicle" class="energy-label">
                                                <input type="checkbox" id="new-energy-vehicle" v-model="isNewEnergyVehicle" @change="calculateFee">
                                                新能源车
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="examples">
                                <button class="btn example-btn" @click="setExample1">
                                    <i class="fas fa-car"></i> 免费停车示例
                                </button>
                                <button class="btn example-btn" @click="setExample2">
                                    <i class="fas fa-car"></i> 跨日收费示例
                                </button>
                                <button class="btn example-btn" @click="setExample3">
                                    <i class="fas fa-car"></i> 封顶收费示例
                                </button>

                                <button class="btn example-btn" @click="setExample4">
                                    <i class="fas fa-car"></i> 2025/01/23 07:00:57——2025/01/24 13:17:15
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="result-panel">
                    <div class="result-display" :class="{
                        'error': resultType === 'error',
                        'success': resultType === 'success'
                    }" id="fee-result">
                        {{ feeResult }}
                    </div>

                    <div class="summary-grid">
                        <div class="summary-card">
                            <div class="summary-title">总停车时间</div>
                            <div class="summary-value" id="summary-total-time">{{ summaryTotalTime }}</div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-title">实际计费时间</div>
                            <div class="summary-value" id="summary-charge-time">{{ summaryChargeTime }}</div>
                        </div>
                        <div class="summary-card stage-card">
                            <div class="summary-title">第一时段费用</div>
                            <div class="summary-value" id="summary-stage1">{{ summaryStage1 }}</div>
                        </div>
                        <div class="summary-card stage-card">
                            <div class="summary-title">第二时段费用</div>
                            <div class="summary-value" id="summary-stage2">{{ summaryStage2 }}</div>
                        </div>
                    </div>

                    <div class="details">
                        <div class="detail-row">
                            <span class="detail-title">总停车时间:</span>
                            <span class="detail-value" id="total-time">{{ totalTime }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">实际计费时间:</span>
                            <span class="detail-value" id="chargeable-time">{{ chargeableTime }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">计费周期:</span>
                            <span class="detail-value" id="billing-periods">{{ billingPeriods }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">第一时段计费:</span>
                            <span class="detail-value stage1" id="first-hour-fee">{{ firstHourFee }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">第二时段计费:</span>
                            <span class="detail-value stage2" id="second-hour-fee">{{ secondHourFee }}</span>
                        </div>
                        <div class="detail-row" id="third-hour-fee-row">
                            <span class="detail-title">第三时段计费:</span>
                            <span class="detail-value stage3" id="third-hour-fee">{{ thirdHourFee }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">每日限价应用:</span>
                            <span class="detail-value" id="cap-applied">{{ capApplied }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">开始时间:</span>
                            <span class="detail-value" id="start-time-display">{{ startTimeDisplay }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-title">结束时间:</span>
                            <span class="detail-value" id="end-time-display">{{ endTimeDisplay }}</span>
                        </div>
                    </div>

                    <div class="billing-list">
                        <div class="billing-title">详细计费清单</div>
                        <div class="billing-header">
                            <div class="billing-time">时间段</div>
                            <div class="billing-duration">时长</div>
                            <div class="billing-fee">费用</div>
                        </div>
                        <div id="billing-items">
                            <div v-if="billingItems.length === 0" class="billing-item free-item">
                                <div class="billing-time">请计算后查看详细清单</div>
                                <div class="billing-duration">-</div>
                                <div class="billing-fee">-</div>
                            </div>

                            <template v-else>
                                <template v-for="(dayItems, day) in groupedBillingItems" :key="day">
                                    <div v-for="(item, index) in dayItems.items" :key="index" class="billing-item" :class="{
                                        'free-item': item.type === 'free',
                                        'stage1-item': item.stage === 1,
                                        'stage2-item': item.stage === 2,
                                        'stage3-item': item.stage === 3
                                    }">
                                        <div class="billing-time">{{ formatDateTime(item.start) }} ~ {{ formatDateTime(item.end) }}</div>
                                        <div class="billing-duration">{{ Math.round(item.duration / 60000) }} 分钟{{ item.unitDesc }}</div>
                                        <div class="billing-fee">
                                            {{ item.type === 'cap' ? '封顶' : (item.fee > 0 ? item.fee.toFixed(2) + ' 元' : '免费') }}
                                        </div>
                                    </div>
                                    <div class="day-total">
                                        <div class="billing-time">{{ day }} 小计</div>
                                        <div class="billing-duration"></div>
                                        <div class="billing-fee">{{ dayItems.total.toFixed(2) }} 元</div>
                                    </div>
                                </template>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <div class="rules">
                <h3>计费规则说明（现行规则）</h3>
                <ul>
                    <li><span class="highlight">当前计费规则</span>: <span class="rule-highlight" id="current-rule">{{ currentRuleName }}</span></li>
                    <li v-if="currentBillingType === 1||currentBillingType === 2||currentBillingType === 3"><span class="highlight">每日免费停放期</span>：<strong>每天首个{{ ruleFreeMinutes }}分钟免费</strong>（独立计算，跨日重新计算）</li>
                    <li v-if="currentBillingType === 4||currentBillingType === 5"><span class="highlight">免费停放期</span>：<strong>首个{{ ruleFreeMinutes }}分钟内免费</strong></li>
                    <li><span class="highlight">第一小时费率</span>：<span id="rule-first-hour">{{ ruleFirstHour }}</span></li>
                    <li><span class="highlight">第二小时费率</span>：<span id="rule-second-hour">{{ ruleSecondHour }}</span></li>
                    <li><span class="highlight">第三小时起费率</span>：<span id="rule-third-hour">{{ ruleThirdHour }}</span></li>
                    <li><span class="highlight">每日限价</span>：<span id="rule-daily-cap">{{ ruleDailyCap }}</span></li>
                    <li><span class="highlight">计费时间段</span>：<span id="rule-charge-time">{{ ruleChargeTime }}</span></li>
                    <li><span class="highlight">每日封顶</span>：<strong>每天费用不会超过封顶金额</strong></li>
                    <li><span class="highlight">时间计算</span>：{{ timeCalculationRule }}</li>
                </ul>
            </div>

            <div style="margin-bottom:20px;">
                <footer>
                    <p>中海基建技术研发小组开发</p>
                </footer>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted } = Vue;

        createApp({
            setup() {
                // 计费规则类型
                const currentBillingType = ref(1);

                // 计费参数
                const freeMinutes = ref(30);
                const firstHourRate = ref(1);
                const secondHourRate = ref(2);
                const thirdHourRate = ref(2.5);
                const dailyCap = ref(60);
                const chargeStart = ref('08:00');
                const chargeEnd = ref('22:00');

                // 时间输入
                const quickStart = ref('');
                const quickEnd = ref('');
                const quickStartError = ref('');
                const quickEndError = ref('');

                // 新能源车选项
                const isNewEnergyVehicle = ref(false);

                // 计算结果
                const feeResult = ref('请输入停车时间并点击计算');
                const resultType = ref('');
                const summaryTotalTime = ref('-');
                const summaryChargeTime = ref('-');
                const summaryStage1 = ref('-');
                const summaryStage2 = ref('-');
                const totalTime = ref('-');
                const chargeableTime = ref('-');
                const billingPeriods = ref('-');
                const firstHourFee = ref('-');
                const secondHourFee = ref('-');
                const thirdHourFee = ref('-');
                const capApplied = ref('-');
                const startTimeDisplay = ref('-');
                const endTimeDisplay = ref('-');
                const billingItems = ref([]);
                const isCalculating = ref(false);

                // 计费规则说明
                const currentRuleName = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '第一类计费规则';
                        case 2: return '第二类计费规则';
                        case 3: return '第三类计费规则';
                        case 4: return '露天停车场计费规则';
                        case 5: return '室内停车场计费规则';
                        default: return '第一类计费规则';
                    }
                });

                const ruleFirstHour = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '1元/15分钟';
                        case 2: return '1.5元/30分钟';
                        case 3: return '1元/30分钟';
                        case 4: return '3元/小时';
                        case 5: return '4元/小时';
                        default: return '1元/15分钟';
                    }
                });

                const ruleSecondHour = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '2元/15分钟';
                        case 2: return '2元/30分钟';
                        case 3: return '1.5元/30分钟';
                        case 4: return '2元/小时';
                        case 5: return '3元/小时';
                        default: return '2元/15分钟';
                    }
                });

                const ruleThirdHour = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '2.5元/15分钟';
                        case 2: return '2.5元/30分钟';
                        case 3: return '2元/30分钟';
                        case 4: return '2元/小时';
                        case 5: return '3元/小时';
                        default: return '2.5元/15分钟';
                    }
                });

                const ruleDailyCap = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '60元';
                        case 2: return '40元';
                        case 3: return '30元';
                        case 4: return '25元';
                        case 5: return '30元';
                        default: return '60元';
                    }
                });

                const ruleChargeTime = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '08:00-22:00';
                        case 2: return '09:00-21:00';
                        case 3: return '09:00-21:00';
                        case 4: return '00:00-23:59';
                        case 5: return '00:00-23:59';
                        default: return '08:00-22:00';
                    }
                });

                const ruleFreeMinutes = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '30';
                        case 2: return '30';
                        case 3: return '30';
                        case 4: return '15';
                        case 5: return '15';
                        default: return '15';
                    }
                });

                const timeCalculationRule = computed(() => {
                    if (currentBillingType.value === 1) {
                        return '系统精确计算到秒，不足15分钟按15分钟计算';
                    } else if (currentBillingType.value === 4 || currentBillingType.value === 5) {
                        return '系统精确计算到秒，不足1小时按1小时计算';
                    } else {
                        return '系统精确计算到秒，不足30分钟按30分钟计算';
                    }
                });

                // 标签和帮助文本
                const firstHourLabel = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '第一小时费率 (元/15分钟)';
                        case 2: case 3: return '第一小时费率 (元/30分钟)';
                        case 4: case 5: return '第一小时费率 (元/小时)';
                        default: return '第一小时费率 (元/15分钟)';
                    }
                });

                const secondHourLabel = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '第二小时费率 (元/15分钟)';
                        case 2: case 3: return '第二小时费率 (元/30分钟)';
                        case 4: case 5: return '第二小时费率 (元/小时)';
                        default: return '第二小时费率 (元/15分钟)';
                    }
                });

                const thirdHourLabel = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '第三小时起费率 (元/15分钟)';
                        case 2: case 3: return '第三小时起费率 (元/30分钟)';
                        case 4: case 5: return '第三小时起费率 (元/小时)';
                        default: return '第三小时起费率 (元/15分钟)';
                    }
                });

                const firstHourHelp = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '超过免费时间后，第一个小时内每15分钟的费率';
                        case 2: case 3: return '超过免费时间后，第一个小时内每30分钟的费率';
                        case 4: case 5: return '超过免费时间后，第一个小时内每小时的费率';
                        default: return '超过免费时间后，第一个小时内每15分钟的费率';
                    }
                });

                const secondHourHelp = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '超过第一小时后，第二个小时内每15分钟的费率';
                        case 2: case 3: return '超过第一小时后，第二个小时内每30分钟的费率';
                        case 4: case 5: return '超过第一小时后，第二个小时内每小时的费率';
                        default: return '超过第一小时后，第二个小时内每15分钟的费率';
                    }
                });

                const thirdHourHelp = computed(() => {
                    switch (currentBillingType.value) {
                        case 1: return '超过两小时后每15分钟的费率';
                        case 2: case 3: return '超过两小时后每30分钟的费率';
                        case 4: case 5: return '超过两小时后每小时的费率';
                        default: return '超过两小时后每15分钟的费率';
                    }
                });

                // 分组计费项
                const groupedBillingItems = computed(() => {
                    const groups = {};
                    billingItems.value.forEach(item => {
                        const date = formatDate(item.date);
                        if (!groups[date]) {
                            groups[date] = {
                                items: [],
                                total: 0
                            };
                        }
                        groups[date].items.push(item);
                        if (item.type !== 'free') {
                            groups[date].total += item.fee;
                        }
                    });
                    return groups;
                });

                const billingRules = {
                    1: { // 一类路段
                        before: { freeMinutes: 30, dailyCap: 60, chargeStart: '08:00', chargeEnd: '22:00', firstHourRate: 1, secondHourRate: 2, thirdHourRate: 2.5 },
                        after: { freeMinutes: 30, dailyCap: 40, chargeStart: '08:00', chargeEnd: '22:00', firstHourRate: 1, secondHourRate: 2, thirdHourRate: 2 }
                    },
                    2: { // 二类路段
                        before: { freeMinutes: 30, dailyCap: 40, chargeStart: '09:00', chargeEnd: '21:00', firstHourRate: 1.5, secondHourRate: 2, thirdHourRate: 2.5 },
                        after: { freeMinutes: 30, dailyCap: 30, chargeStart: '09:00', chargeEnd: '21:00', firstHourRate: 1.5, secondHourRate: 2, thirdHourRate: 2 }
                    },
                    3: { // 三类路段
                        before: { freeMinutes: 30, dailyCap: 30, chargeStart: '09:00', chargeEnd: '21:00', firstHourRate: 1, secondHourRate: 1.5, thirdHourRate: 2 },
                        after: { freeMinutes: 30, dailyCap: 20, chargeStart: '09:00', chargeEnd: '21:00', firstHourRate: 1, secondHourRate: 1.5, thirdHourRate: 1.5 }
                    },
                    4: { // 露天停车场
                        before: { freeMinutes: 15, dailyCap: 25, chargeStart: '00:00', chargeEnd: '23:59', firstHourRate: 3, secondHourRate: 2, thirdHourRate: 2 },
                        after: { freeMinutes: 30, dailyCap: 20, chargeStart: '00:00', chargeEnd: '23:59', firstHourRate: 1.5, secondHourRate: 1.5, thirdHourRate: 1.5 }
                    },
                    5: { // 室内停车场
                        before: { freeMinutes: 15, dailyCap: 30, chargeStart: '00:00', chargeEnd: '23:59', firstHourRate: 4, secondHourRate: 3, thirdHourRate: 3 },
                        after: { freeMinutes: 30, dailyCap: 25, chargeStart: '00:00', chargeEnd: '23:59', firstHourRate: 2, secondHourRate: 2, thirdHourRate: 2 }
                    }
                };

                

                // 修改setBillingType函数
                const setBillingType = (type) => {
                    currentBillingType.value = type;

                    // 根据当前降费配置选择规则
                    const rule = billingRules[type][discountType.value];

                    // 设置参数
                    freeMinutes.value = rule.freeMinutes;
                    dailyCap.value = rule.dailyCap;
                    chargeStart.value = rule.chargeStart;
                    chargeEnd.value = rule.chargeEnd;
                    firstHourRate.value = rule.firstHourRate;
                    secondHourRate.value = rule.secondHourRate;
                    thirdHourRate.value = rule.thirdHourRate;
                };

                const setDiscountType = (type) => {
                    discountType.value = type;
                    // 重新设置当前计费规则，以更新参数
                    setBillingType(currentBillingType.value);
                };

                // 格式化日期为YYYY-MM-DD
                const formatDate = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                // 格式化时间显示（带秒）
                const formatTime = (date) => {
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${hours}:${minutes}:${seconds}`;
                };

                // 格式化日期时间（完整）
                const formatDateTime = (date) => {
                    return `${formatDate(date)} ${formatTime(date)}`;
                };

                // 格式持续时间
                const formatDuration = (minutes) => {
                    if (minutes < 60) return `${minutes} 分钟`;
                    const hours = Math.floor(minutes / 60);
                    const mins = minutes % 60;
                    return `${hours} 小时 ${mins} 分钟`;
                };

                // 从快速输入框解析日期时间
                const parseQuickTime = (value) => {
                    const regex = /^(\d{4})[-/](\d{1,2})[-/](\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
                    const match = value.match(regex);

                    if (!match) {
                        return null;
                    }

                    const year = parseInt(match[1], 10);
                    const month = parseInt(match[2], 10) - 1;
                    const day = parseInt(match[3], 10);
                    const hours = parseInt(match[4], 10);
                    const minutes = parseInt(match[5], 10);
                    const seconds = parseInt(match[6], 10);

                    if (
                        month < 0 || month > 11 ||
                        day < 1 || day > 31 ||
                        hours < 0 || hours > 23 ||
                        minutes < 0 || minutes > 59 ||
                        seconds < 0 || seconds > 59
                    ) {
                        return null;
                    }

                    const date = new Date(year, month, day, hours, minutes, seconds);

                    const isValidDate = (
                        date.getFullYear() === year &&
                        date.getMonth() === month &&
                        date.getDate() === day &&
                        date.getHours() === hours &&
                        date.getMinutes() === minutes &&
                        date.getSeconds() === seconds
                    );

                    return isValidDate ? date : null;
                };

                // 设置示例1：免费停车
                const setExample1 = () => {
                    const today = new Date();
                    const startDate = new Date(today);
                    const endDate = new Date(today);
                    startDate.setHours(18, 15, 0);
                    endDate.setHours(18, 29, 59);

                    quickStart.value = formatDateTime(startDate);
                    quickEnd.value = formatDateTime(endDate);

                    calculateFee();
                };

                // 设置示例2：跨日收费
                const setExample2 = () => {
                    const today = new Date();
                    const startDate = new Date(today);
                    startDate.setHours(21, 0, 0);

                    const endDate = new Date(today);
                    endDate.setDate(endDate.getDate() + 1);
                    endDate.setHours(9, 31, 0);

                    quickStart.value = formatDateTime(startDate);
                    quickEnd.value = formatDateTime(endDate);

                    calculateFee();
                };

                // 设置示例3：三阶段计费并封顶
                const setExample3 = () => {
                    const today = new Date();
                    const startDate = new Date(today);
                    startDate.setHours(8, 0, 0);

                    const endDate = new Date(today);
                    endDate.setHours(22, 0, 0);

                    quickStart.value = formatDateTime(startDate);
                    quickEnd.value = formatDateTime(endDate);

                    calculateFee();
                };

                // 设置示例4
                const setExample4 = () => {
                    const startDate = new Date(2025, 0, 23, 7, 0, 57);
                    const endDate = new Date(2025, 0, 24, 13, 17, 15);

                    quickStart.value = formatDateTime(startDate);
                    quickEnd.value = formatDateTime(endDate);

                    calculateFee();
                };

                // 核心计费函数 - 露天停车场和室内停车场专用（连续24小时封顶规则）
                const calculateParkingLotFee = (startTime, endTime, freeMinutes, firstHourRate, secondHourRate, thirdHourRate, dailyCap) => {
                    const billingItems = [];
                    let totalFee = 0;
                    const totalMinutes = Math.round((endTime - startTime) / (1000 * 60));
                    let chargeableMinutes = 0;
                    let periods = 0;
                    let firstHourFee = 0;
                    let secondHourFee = 0;
                    let thirdHourFee = 0;
                    let dailyCapApplied = [];
                    let capSavings = 0;

                    // 判断总停车时间是否在免费时间内
                    if (totalMinutes <= freeMinutes) {
                        // 整个停车时间段都是免费的
                        billingItems.push({
                            date: new Date(startTime),
                            start: startTime,
                            end: endTime,
                            duration: endTime - startTime,
                            fee: 0,
                            type: 'free',
                            stage: 0
                        });
                        return {
                            totalFee: 0,
                            totalMinutes,
                            chargeableMinutes: 0,
                            periods: 0,
                            firstHourFee: 0,
                            secondHourFee: 0,
                            thirdHourFee: 0,
                            billingItems,
                            dailyCapApplied: [],
                            capSavings: 0
                        };
                    }

                    // 初始化24小时封顶周期
                    let currentTime = new Date(startTime);  // 从开始时间就计费（不再扣除免费时间）
                    let accumulatedHours = 0; // 累计计费小时数（用于费率阶段）
                    let periodStart = new Date(startTime); // 当前24小时周期开始时间
                    let periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60000); // 当前24小时周期结束时间
                    let currentPeriodFee = 0; // 当前周期累计费用

                    while (currentTime < endTime) {
                        // 计算当前小时结束时间（整点计算）
                        let hourEnd = new Date(currentTime.getTime() + 60 * 60000);
                        hourEnd = new Date(Math.min(hourEnd, endTime, periodEnd));

                        const hourMinutes = Math.ceil((hourEnd - currentTime) / (1000 * 60));

                        // 确定当前小时费率阶段
                        let hourRate;
                        let hourStage;
                        if (accumulatedHours < 1) {
                            hourRate = firstHourRate;
                            hourStage = 1;
                        } else if (accumulatedHours < 2) {
                            hourRate = secondHourRate;
                            hourStage = 2;
                        } else {
                            hourRate = thirdHourRate;
                            hourStage = 3;
                        }

                        // 检查是否超过周期封顶
                        const periodFeeRemaining = dailyCap - currentPeriodFee;
                        let actualHourFee;

                        if (periodFeeRemaining <= 0) {
                            // 当前周期已达封顶，本小时免费
                            actualHourFee = 0;
                            capSavings += hourRate;
                            billingItems.push({
                                date: new Date(periodStart),
                                start: currentTime,
                                end: hourEnd,
                                duration: hourEnd - currentTime,
                                fee: 0,
                                type: 'cap',
                                stage: hourStage,
                                unitDesc: " (1小时计费单位)"
                            });
                        } else if (hourRate > periodFeeRemaining) {
                            // 部分收费（未达封顶的部分）
                            actualHourFee = periodFeeRemaining;
                            capSavings += hourRate - periodFeeRemaining;

                            // 添加封顶项
                            billingItems.push({
                                date: new Date(periodStart),
                                start: currentTime,
                                end: hourEnd,
                                duration: hourEnd - currentTime,
                                fee: actualHourFee,
                                type: 'cap',
                                stage: hourStage,
                                unitDesc: " (1小时计费单位)"
                            });
                        } else {
                            // 正常收费
                            actualHourFee = hourRate;
                            billingItems.push({
                                date: new Date(periodStart),
                                start: currentTime,
                                end: hourEnd,
                                duration: hourEnd - currentTime,
                                fee: actualHourFee,
                                type: 'charge',
                                stage: hourStage,
                                unitDesc: " (1小时计费单位)"
                            });
                        }

                        // 更新累计数据
                        totalFee += actualHourFee;
                        currentPeriodFee += actualHourFee;
                        chargeableMinutes += hourMinutes;
                        periods++;
                        accumulatedHours++;

                        // 更新阶段费用统计
                        if (hourStage === 1) firstHourFee += actualHourFee;
                        else if (hourStage === 2) secondHourFee += actualHourFee;
                        else thirdHourFee += actualHourFee;

                        // 移动当前时间到小时结束
                        currentTime = hourEnd;

                        // 检查是否进入新周期（达到24小时）
                        if (currentTime >= periodEnd && currentTime < endTime) {
                            // 开始新计费周期
                            periodStart = new Date(periodEnd);
                            periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60000);
                            currentPeriodFee = 0;

                            // 重置累计小时数为0
                            accumulatedHours = 0;

                            // 记录封顶周期
                            dailyCapApplied.push(formatDate(periodStart));
                        }
                    }

                    return {
                        totalFee,
                        totalMinutes,
                        chargeableMinutes,
                        periods,
                        firstHourFee,
                        secondHourFee,
                        thirdHourFee,
                        billingItems,
                        dailyCapApplied,
                        capSavings
                    };
                };

                // 核心计费函数 - 原始规则
                const calculateOriginalFee = (startTime, endTime, freeMinutes, firstHourRate, secondHourRate, thirdHourRate, dailyCap, chargeStartStr, chargeEndStr) => {
                    // 根据计费规则类型确定计费时间段长度
                    let segmentMinutes;
                    if (currentBillingType.value === 1) {
                        segmentMinutes = 15;
                    } else if (currentBillingType.value === 2 || currentBillingType.value === 3) {
                        segmentMinutes = 30;
                    } else if (currentBillingType.value === 4 || currentBillingType.value === 5) {
                        segmentMinutes = 60; // 露天和室内停车场按小时计费
                    }

                    const billingItems = [];
                    let totalFee = 0;
                    let totalMinutes = Math.round((endTime - startTime) / (1000 * 60));
                    let chargeableMinutes = 0;
                    let periods = 0;
                    let firstHourFee = 0;
                    let secondHourFee = 0;
                    let thirdHourFee = 0;
                    let firstHourUnits = 0;
                    let secondHourUnits = 0;
                    let thirdHourUnits = 0;
                    let dailyCapApplied = [];
                    let capSavings = 0;

                    // 解析每日计费时间段
                    const [chargeStartHour, chargeStartMinute] = chargeStartStr.split(':').map(Number);
                    const [chargeEndHour, chargeEndMinute] = chargeEndStr.split(':').map(Number);

                    // 计算每天的计费
                    let currentDay = new Date(startTime);
                    currentDay.setHours(0, 0, 0, 0); // 设置为当天00:00:00

                    while (currentDay < endTime) {
                        // 准备当天的变量
                        const dayStart = new Date(currentDay);
                        const dayEnd = new Date(currentDay);
                        dayEnd.setDate(dayEnd.getDate() + 1);
                        dayEnd.setSeconds(-1); // 设置为当天的23:59:59

                        // 当天实际的停车时间段
                        const segStart = new Date(Math.max(startTime, dayStart));
                        const segEnd = new Date(Math.min(endTime, dayEnd));

                        // 设置当天的计费时间段
                        const chargeStartDay = new Date(dayStart);
                        chargeStartDay.setHours(chargeStartHour, chargeStartMinute, 0, 0);

                        const chargeEndDay = new Date(dayStart);
                        chargeEndDay.setHours(chargeEndHour, chargeEndMinute, 0, 0);

                        // 如果计费结束时间早于开始时间（跨天），调整为次日结束时间
                        if (chargeEndDay < chargeStartDay) {
                            chargeEndDay.setDate(chargeEndDay.getDate() + 1);
                        }

                        // 重置每天的费用阶段计数
                        let dayStageMinutes = 0;
                        let stage = 1;
                        let dailyFreeUsed = false;
                        let dailyFee = 0;  // 当天总费用
                        let isCapped = false; // 标记当天是否已达到封顶

                        // 只有当天有停车时间才处理
                        if (segStart < segEnd) {
                            // 计算当天的免费时间段 (只在计费时间段内有效)
                            const freeStart = new Date(Math.max(segStart, chargeStartDay));
                            const freeEnd = new Date(freeStart);
                            freeEnd.setMinutes(freeEnd.getMinutes() + freeMinutes);

                            // 实际免费结束时间取最小（不能超过计费结束时间和当天停车结束时间）
                            const actualFreeEnd = new Date(Math.min(freeEnd, chargeEndDay, segEnd));

                            // 记录免费时间段（如果有）
                            if (freeStart < actualFreeEnd) {
                                billingItems.push({
                                    date: new Date(currentDay),
                                    start: freeStart,
                                    end: actualFreeEnd,
                                    duration: actualFreeEnd - freeStart,
                                    fee: 0,
                                    type: 'free',
                                    stage: 0
                                });
                                dailyFreeUsed = true;
                            }

                            // 当天计费的开始时间（从免费结束后开始或从最早计费时间开始）
                            let billingStart = dailyFreeUsed ? actualFreeEnd : new Date(freeStart);

                            // 只在当天计费时间段内计费
                            while (billingStart < segEnd && billingStart < chargeEndDay) {
                                // 如果当天费用已达上限，不再计算后续时间段的费用
                                if (isCapped) {
                                    // 确定当前计费段结束时间
                                    let segmentEnd = new Date(billingStart);
                                    segmentEnd.setMinutes(segmentEnd.getMinutes() + segmentMinutes);
                                    segmentEnd = new Date(Math.min(segmentEnd, segEnd, chargeEndDay));

                                    // 计算持续时间（分钟）
                                    const segmentDuration = (segmentEnd - billingStart) / 60000;
                                    dayStageMinutes += segmentDuration;

                                    // 确定当前阶段
                                    if (dayStageMinutes > 120) stage = 3;
                                    else if (dayStageMinutes > 60) stage = 2;
                                    else stage = 1;

                                    // 计算应收费用（根据规则类型）
                                    let segmentFeeRate;
                                    switch (stage) {
                                        case 1: segmentFeeRate = firstHourRate; break;
                                        case 2: segmentFeeRate = secondHourRate; break;
                                        case 3: segmentFeeRate = thirdHourRate; break;
                                    }

                                    // 将应收费用计入节省金额
                                    capSavings += segmentFeeRate;

                                    // 记录计费项（节省部分）
                                    billingItems.push({
                                        date: new Date(currentDay),
                                        start: new Date(billingStart),
                                        end: new Date(segmentEnd),
                                        duration: segmentEnd - billingStart,
                                        fee: 0,
                                        type: 'cap',
                                        stage: stage
                                    });

                                    // 移动到下一个时间段开始
                                    billingStart = new Date(segmentEnd);
                                    continue;
                                }

                                // 确定当前计费段结束时间（30分钟或15分钟）
                                let segmentEnd = new Date(billingStart);
                                segmentEnd.setMinutes(segmentEnd.getMinutes() + segmentMinutes);

                                // 不超过停车结束时间和计费结束时间
                                segmentEnd = new Date(Math.min(segmentEnd, segEnd, chargeEndDay));

                                // 如果当前时间段没有实际时长，退出
                                if (billingStart.getTime() >= segmentEnd.getTime()) break;

                                // 计算持续时间（分钟）
                                const segmentDuration = (segmentEnd - billingStart) / 60000;

                                // 根据当前阶段计算费率
                                dayStageMinutes += segmentDuration;

                                // 确定当前阶段
                                if (dayStageMinutes > 120) { // 超过2小时
                                    stage = 3;
                                } else if (dayStageMinutes > 60) { // 超过1小时
                                    stage = 2;
                                } else {
                                    stage = 1;
                                }

                                // 当前时段的费率（直接使用每30分钟的费用）
                                let segmentFeeRate;
                                switch (stage) {
                                    case 1: segmentFeeRate = firstHourRate; break;
                                    case 2: segmentFeeRate = secondHourRate; break;
                                    case 3: segmentFeeRate = thirdHourRate; break;
                                    default: segmentFeeRate = thirdHourRate;
                                }

                                // 判断加上本时段后是否会超过当天封顶费用
                                const proposedFee = segmentFeeRate;
                                const afterFee = dailyFee + proposedFee;

                                // 如果超过封顶值，则只收取剩余未达到封顶的部分
                                if (afterFee > dailyCap) {
                                    const partialFee = dailyCap - dailyFee;

                                    // 记录计费项（部分收费）
                                    const itemType = partialFee > 0 ? 'charge' : 'cap'; // 根据费用决定类型
                                    billingItems.push({
                                        date: new Date(currentDay),
                                        start: new Date(billingStart),
                                        end: new Date(segmentEnd),
                                        duration: segmentEnd - billingStart,
                                        fee: partialFee,
                                        type: itemType,  // 使用动态类型
                                        stage: stage
                                    });

                                    // 更新计数器
                                    chargeableMinutes += segmentDuration;
                                    periods++;
                                    dailyFee = dailyCap; // 设置为封顶值
                                    totalFee += partialFee;
                                    capSavings += proposedFee - partialFee;

                                    // 更新阶段计费统计
                                    if (stage === 1) {
                                        firstHourFee += partialFee;
                                        // 只有当收取完整费用时才增加单位计数
                                        if (partialFee === segmentFeeRate) {
                                            firstHourUnits++;
                                        }
                                    } else if (stage === 2) {
                                        secondHourFee += partialFee;
                                        if (partialFee === segmentFeeRate) {
                                            secondHourUnits++;
                                        }
                                    } else if (stage === 3) {
                                        thirdHourFee += partialFee;
                                        if (partialFee === segmentFeeRate) {
                                            thirdHourUnits++;
                                        }
                                    }

                                    // 标记当天已达到封顶
                                    isCapped = true;

                                    // 移动到下一个时间段开始
                                    billingStart = new Date(segmentEnd);
                                    continue;
                                }

                                // 记录计费项
                                billingItems.push({
                                    date: new Date(currentDay),
                                    start: new Date(billingStart),
                                    end: new Date(segmentEnd),
                                    duration: segmentEnd - billingStart,
                                    fee: segmentFeeRate,
                                    type: 'charge',
                                    stage: stage
                                });

                                // 更新计数器
                                chargeableMinutes += segmentDuration;
                                periods++;
                                dailyFee += segmentFeeRate;
                                totalFee += segmentFeeRate;

                                // 更新阶段计费统计
                                if (stage === 1) {
                                    firstHourFee += segmentFeeRate;
                                    // 总是增加单位计数，因为收取完整费用
                                    firstHourUnits++;
                                } else if (stage === 2) {
                                    secondHourFee += segmentFeeRate;
                                    secondHourUnits++;
                                } else if (stage === 3) {
                                    thirdHourFee += segmentFeeRate;
                                    thirdHourUnits++;
                                }

                                // 移动到下一个时间段开始
                                billingStart = new Date(segmentEnd);
                            }

                            // 如果当天达到上限，记录
                            if (isCapped) {
                                dailyCapApplied.push(formatDate(currentDay));
                            }
                        }

                        // 移到下一天
                        currentDay.setDate(currentDay.getDate() + 1);
                        currentDay.setHours(0, 0, 0, 0);
                    }

                    return {
                        totalFee,
                        totalMinutes,
                        chargeableMinutes,
                        periods,
                        firstHourFee,
                        secondHourFee,
                        thirdHourFee,
                        firstHourUnits,
                        secondHourUnits,
                        thirdHourUnits,
                        billingItems,
                        dailyCapApplied,
                        capSavings
                    };
                }

                // 计算停车费
                const calculateFee = async () => {
                    isCalculating.value = true;
                    billingItems.value = [];

                    try {
                        // 验证快速输入时间
                        const startTime = parseQuickTime(quickStart.value);
                        const endTime = parseQuickTime(quickEnd.value);

                        if (!startTime) {
                            quickStartError.value = "时间格式无效，请使用 YYYY-MM-DD HH:mm:ss";
                            return;
                        } else {
                            quickStartError.value = '';
                        }

                        if (!endTime) {
                            quickEndError.value = "时间格式无效，请使用 YYYY-MM-DD HH:mm:ss";
                            return;
                        } else {
                            quickEndError.value = '';
                        }

                        if (startTime >= endTime) {
                            throw new Error('结束时间必须晚于开始时间');
                        }

                        // 显示精确时间
                        startTimeDisplay.value = formatDateTime(startTime);
                        endTimeDisplay.value = formatDateTime(endTime);

                        let result;

                        // 根据计费类型选择不同的计费算法
                        if (currentBillingType.value === 4 || currentBillingType.value === 5) {
                            // 露天停车场和室内停车场使用新算法（按小时拆分）
                            result = calculateParkingLotFee(
                                startTime,
                                endTime,
                                freeMinutes.value,
                                firstHourRate.value,
                                secondHourRate.value,
                                thirdHourRate.value,
                                dailyCap.value
                            );
                        } else {
                            // 其他类型使用原始算法
                            result = calculateOriginalFee(
                                startTime,
                                endTime,
                                freeMinutes.value,
                                firstHourRate.value,
                                secondHourRate.value,
                                thirdHourRate.value,
                                dailyCap.value,
                                chargeStart.value,
                                chargeEnd.value
                            );
                        }

                        // 获取新能源车选项状态
                        // 应用新能源车折扣
                        let discountMessage = "";
                        let discountedFee = result.totalFee;

                        if (isNewEnergyVehicle.value) {
                            discountedFee = result.totalFee * 0.5;
                            discountMessage = ` (新能源5折，停车费${discountedFee.toFixed(2)}元)`;
                        }

                        // 显示结果
                        feeResult.value = `总停车费: ${result.totalFee.toFixed(2)} 元${discountMessage}`;
                        resultType.value = "success";

                        // 显示详细结果
                        const roundedTotalMinutes = Math.round(result.totalMinutes);
                        totalTime.value = `${roundedTotalMinutes} 分钟 (${formatDuration(roundedTotalMinutes)})`;
                        summaryTotalTime.value = `${formatDuration(roundedTotalMinutes)}`;

                        const roundedChargeableMinutes = Math.round(result.chargeableMinutes);
                        chargeableTime.value = `${roundedChargeableMinutes} 分钟 (${formatDuration(roundedChargeableMinutes)})`;
                        summaryChargeTime.value = `${formatDuration(roundedChargeableMinutes)}`;

                        billingPeriods.value = `共 ${result.periods} 个计费时间段`;

                        // 根据计费类型显示不同的阶段费用
                        if (currentBillingType.value === 4 || currentBillingType.value === 5) {
                            firstHourFee.value = `${result.firstHourFee.toFixed(2)} 元`;
                            secondHourFee.value = `${result.secondHourFee.toFixed(2)} 元`;
                            thirdHourFee.value = `${result.thirdHourFee.toFixed(2)} 元`;
                        } else {
                            firstHourFee.value = `${result.firstHourFee.toFixed(2)} 元 (${result.firstHourUnits} 个单位 × ${firstHourRate.value} 元)`;
                            secondHourFee.value = `${result.secondHourFee.toFixed(2)} 元 (${result.secondHourUnits} 个单位 × ${secondHourRate.value} 元)`;
                            thirdHourFee.value = `${result.thirdHourFee.toFixed(2)} 元 (${result.thirdHourUnits} 个单位 × ${thirdHourRate.value} 元)`;
                        }

                        summaryStage1.value = `${result.firstHourFee.toFixed(2)} 元`;
                        summaryStage2.value = `${result.secondHourFee.toFixed(2)} 元`;

                        capApplied.value = result.dailyCapApplied.length > 0
                            ? `${result.dailyCapApplied.length} 天达到上限，节省 ${result.capSavings.toFixed(2)} 元`
                            : "无";

                        // 添加单位描述
                        let unitDesc = "";
                        if (currentBillingType.value === 1) {
                            unitDesc = " (15分钟计费单位)";
                        } else if (currentBillingType.value === 2 || currentBillingType.value === 3) {
                            unitDesc = " (30分钟计费单位)";
                        } else if (currentBillingType.value === 4 || currentBillingType.value === 5) {
                            unitDesc = " (1小时计费单位)";
                        }

                        // 添加单位描述到计费项
                        result.billingItems.forEach(item => {
                            item.unitDesc = unitDesc;
                        });

                        billingItems.value = result.billingItems;

                    } catch (e) {
                        feeResult.value = `错误: ${e.message}`;
                        resultType.value = "error";
                    } finally {
                        isCalculating.value = false;
                    }
                };

                // 重置结果
                const resetResults = () => {
                    feeResult.value = "请输入停车时间并点击计算";
                    resultType.value = "";
                    summaryTotalTime.value = "-";
                    summaryChargeTime.value = "-";
                    summaryStage1.value = "-";
                    summaryStage2.value = "-";
                    totalTime.value = "-";
                    chargeableTime.value = "-";
                    billingPeriods.value = "-";
                    firstHourFee.value = "-";
                    secondHourFee.value = "-";
                    thirdHourFee.value = "-";
                    capApplied.value = "-";
                    startTimeDisplay.value = "-";
                    endTimeDisplay.value = "-";
                    billingItems.value = [];
                };
                //降费配置
                const discountType = ref('before'); // 'before' 或 'after'

               
                // 初始化
                onMounted(() => {
                    const today = new Date();
                    const tomorrow = new Date(today);
                    tomorrow.setDate(tomorrow.getDate() + 1);

                    quickStart.value = `${formatDate(today)} 08:00:00`;
                    quickEnd.value = `${formatDate(tomorrow)} 18:00:00`;

                    discountType.value = 'before'; // 默认使用降费前配置

                    setBillingType(1);
                });

                return {
                    currentBillingType,
                    freeMinutes,
                    firstHourRate,
                    secondHourRate,
                    thirdHourRate,
                    dailyCap,
                    chargeStart,
                    chargeEnd,
                    quickStart,
                    quickEnd,
                    quickStartError,
                    quickEndError,
                    isNewEnergyVehicle,
                    feeResult,
                    resultType,
                    summaryTotalTime,
                    summaryChargeTime,
                    summaryStage1,
                    summaryStage2,
                    totalTime,
                    chargeableTime,
                    billingPeriods,
                    firstHourFee,
                    secondHourFee,
                    thirdHourFee,
                    capApplied,
                    startTimeDisplay,
                    endTimeDisplay,
                    billingItems,
                    isCalculating,
                    currentRuleName,
                    ruleFirstHour,
                    ruleSecondHour,
                    ruleThirdHour,
                    ruleDailyCap,
                    ruleChargeTime,
                    ruleFreeMinutes,
                    timeCalculationRule,
                    firstHourLabel,
                    secondHourLabel,
                    thirdHourLabel,
                    firstHourHelp,
                    secondHourHelp,
                    thirdHourHelp,
                    groupedBillingItems,
                    setBillingType,
                    setExample1,
                    setExample2,
                    setExample3,
                    setExample4,
                    calculateFee,
                    setDiscountType,
                    discountType,
                    formatDateTime
                };
            }
        }).mount('#app');</script>

</body>
</html>